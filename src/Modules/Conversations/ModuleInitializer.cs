using System.Reflection;
using Conversations.Application.Abstractions;
using Conversations.Application.Calls.CallHubs;
using Conversations.Application.Channels.WhatsApp;
using Conversations.Application.Chats.ChatHub;
using Conversations.Infrastructure.BackgroundServices;
using Conversations.Infrastructure.Data;
using Conversations.Infrastructure.External;
using Conversations.Infrastructure.External.ThreeCXIntegration;
using Conversations.Infrastructure.External.WhatsAppIntegration;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http.Connections;
using Microsoft.AspNetCore.OData;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Shared.Endpoints;

namespace Conversations;

public static class ModuleInitializer
{
    public static IServiceCollection AddConversationsModule(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("ApplicationConnection");
        var assembly = Assembly.GetExecutingAssembly();
        services.AddDbContext<ConversationDbContext>(opt =>
            opt.UseSqlServer(connectionString, b =>
            {
                b.MigrationsHistoryTable(HistoryRepository.DefaultTableName, "Conversations");
                b.MigrationsAssembly(assembly.FullName);
                b.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
            }));
        services.AddScoped<IConversationDbContext>(provider => provider.GetRequiredService<ConversationDbContext>());
        services.AddEndpoints(assembly);
        services.AddHostedService<AutoDialerAutoStartService>();
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));
        services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());
        services.AddControllers().AddOData(options => options.Select().Filter().OrderBy().Expand().Count().SetMaxTop(100));

        services.Configure<WhatsAppSettings>(configuration.GetSection("Integrations:WhatsApp"));

        services.AddScoped<WhatsAppWebhookHandler>();
        services.AddScoped<IMessageSenderFactory, MessageSenderFactory>();
        services.AddScoped<WhatsAppService>();
        services.AddHttpClient<IWhatsAppApiClient, WhatsAppApiClient>();
        services.AddScoped<IChatNotificationService, ChatNotificationService>();
        services.AddScoped<IThreeCXService, ThreeCXService>();
        services.AddScoped<IThreeCXQueueService, ThreeCXQueueService>();

        return services;
    }
    public static IApplicationBuilder UseConversationsModule(this IApplicationBuilder app)
    {
        app.UseEndpoints(endpoints =>
        {
            endpoints.MapHub<ChatHub>("/hubs/chathub", options => options.Transports = HttpTransportType.WebSockets | HttpTransportType.LongPolling);
            endpoints.MapHub<CallHub>("/hubs/callhub", options => options.Transports = HttpTransportType.WebSockets | HttpTransportType.LongPolling);
            endpoints.MapControllers();
        });
        return app;
    }
}
